<template>
  <section>
    <a-card size="small" title="合同与协议" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <!-- 业务类型 -->
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in productClassify.businessType" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 合同号 -->
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="fieldDisabled" size="small" v-model:value="formData.contractNo"/>
          </a-form-item>
          <!-- 买方 -->
          <a-form-item name="buyer" :label="'买方'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.buyer" id="buyer">
              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 供应商 -->
          <a-form-item name="supplier" :label="'供应商'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.supplier" id="supplier">
              <a-select-option v-for="item in sellerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 签约日期 -->
          <a-form-item name="signingDate" :label="'签约日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="fieldDisabled"
              v-model:value="formData.signingDate"
              id="signingDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 国内委托方 -->
          <a-form-item name="domesticPrincipal" :label="'国内委托方'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.domesticPrincipal" id="domesticPrincipal">
              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 运输方式 -->
          <a-form-item name="transportMode" :label="'运输方式'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.transportMode" id="transportMode">
              <a-select-option v-for="item in transportModeOptions" :key="item.label"
                               :value="item.value" :label="item.label">
                {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 合同生效期 -->
          <a-form-item name="effectiveDate" :label="'合同生效期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="fieldDisabled"
              v-model:value="formData.effectiveDate"
              id="effectiveDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 合同有效期 -->
          <a-form-item name="expiryDate" :label="'合同有效期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="fieldDisabled"
              v-model:value="formData.expiryDate"
              id="expiryDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 签约地点(中文) -->
          <a-form-item name="signingLocationCn" :label="'签约地点(中文)'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.signingLocationCn" id="signingLocationCn"
                       @change="handleCityChange">
              <a-select-option v-for="item in cityOptions" :key="item.label"
                               :value="item.value" :label="item.label">
                {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 签约地点(英文) -->
          <a-form-item name="signingLocationEn" :label="'签约地点(英文)'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.signingLocationEn"/>
          </a-form-item>
          <!-- 装运港 -->
          <a-form-item name="portOfLoading" :label="'装运港'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.portOfLoading" id="portOfLoading">
              <a-select-option v-for="item in customsPortOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>


          <!-- 目的港 -->
          <a-form-item name="portOfDestination" :label="'目的港'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.portOfDestination" id="portOfDestination">
              <a-select-option v-for="item in customsPortOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 报关口岸 -->
          <a-form-item name="customsPort" :label="'报关口岸'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.customsPort" id="customsPort">
              <a-select-option v-for="item in customsPortOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 付款方式 -->
          <a-form-item name="paymentMethod" :label="'付款方式'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.paymentMethod" id="paymentMethod">
              <a-select-option v-for="item in productClassify.paymentMethodMap" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 币种 -->
          <a-form-item name="currency" :label="'币种'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.currency" id="currency">
              <a-select-option v-for="item in currencyOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 价格条款 -->
          <a-form-item name="priceTerm" :label="'价格条款'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.priceTerm" id="priceTerm">
              <a-select-option class="cs-select-dropdown" v-for="item in priceTermList"  :key="item.label  " :value="item.value" :label="item.label">
                {{item.label }}
              </a-select-option>
            </cs-select>
<!--            <a-input :disabled="fieldDisabled" size="small" v-model:value="formData.priceTerm"/>-->
          </a-form-item>
          <!-- 价格条款对应港口 -->
          <a-form-item name="priceTermPort" :label="'价格条款对应港口'" class="grid-item" :colon="false">
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.priceTermPort" id="priceTermPort">
              <a-select-option v-for="item in priceTermPortOptions" :key="item.label"
                               :value="item.value" :label="item.label">
                {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 建议授权签约人 -->
          <a-form-item name="suggestedSigner" :label="'建议授权签约人'" class="grid-item" :colon="false">
            <a-input :disabled="fieldDisabled" size="small" v-model:value="formData.suggestedSigner"/>
          </a-form-item>
          <!-- 短溢数% -->
          <a-form-item name="shortageOverflowPercent" :label="'短溢数%'" class="grid-item" :colon="false">
            <a-input-number :disabled="fieldDisabled" size="small" v-model:value="formData.shortageOverflowPercent"
                           style="width: 100%" :precision="6" :controls="false" :min="0"/>
          </a-form-item>
          <!-- 备注 -->
          <a-form-item name="remarks" :label="'备注'" class="grid-item merge-3" :colon="false">
            <a-textarea :disabled="fieldDisabled" size="small" v-model:value="formData.remarks" :autosize="{ minRows: 3, maxRows: 10 }"/>
          </a-form-item>
          <!-- 版本号 -->
          <a-form-item name="versionNo" :label="'版本号'" class="grid-item" :colon="false">
            <a-input disabled size="small" v-model:value="formData.versionNo"/>
          </a-form-item>
          <!-- 制单人 -->
          <a-form-item name="createtUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input disabled size="small" v-model:value="formData.createtUserName"/>
          </a-form-item>
          <!-- 制单时间 -->
          <a-form-item name="createTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              disabled
              v-model:value="formData.insertTime"
              id="createTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              placeholder=""
              size="small"
              style="width: 100%"
            />
          </a-form-item>
          <!-- 单据状态 -->
          <a-form-item name="status" :label="'单据状态'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.data_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 确认时间 -->
          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              disabled
              v-model:value="formData.confirmTime"
              id="confirmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="zhCN"
              size="small"
              style="width: 100%"
              placeholder=""
              showTime
            />
          </a-form-item>
          <!-- 审批状态 -->
          <a-form-item name="apprStatus" :label="'审批状态'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.apprStatus" id="apprStatus">
              <a-select-option v-for="item in productClassify.approval_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW" :disabled="buttonDisabled">保存
            </a-button>
            <a-button size="small" type="primary" @click="handlerConfirm" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.EDIT && formData.status === '0'"
                      :disabled="buttonDisabled" :loading="confirmLoading">
              确认
            </a-button>
            <!--            <a-button size="small" type="primary" @click="handlerSaveClose" class="cs-margin-right"
                                  v-show="props.editConfig.editStatus !== editStatus.SHOW">保存关闭
                        </a-button>-->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>

    <a-card size="small" title="划款信息" class="cs-card-form" :bordered="false">
      <template #extra>
        <a-button type="link" @click="toggleFeeCard">
          {{ showFeeCard ? '收起' : '展开' }}
        </a-button>
      </template>
      <div v-show="showFeeCard" class="cs-form">
        <a-form ref="feeFormRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :model="formData" class="grid-container">
          <!-- 合同金额 -->
          <a-form-item name="contractAmount" :label="'合同金额'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.contractAmount" style="width: 100%" :precision="6"/>
          </a-form-item>
          <!-- 汇率 -->
          <a-form-item name="exchangeRate" :label="'汇率'" class="grid-item" :colon="false">
            <a-input-number
              disabled
              size="small"
              v-model:value="formData.exchangeRate"
              style="width: 100%"
              :precision="6"
              :controls="false"
              :min="0"
              :formatter="value => value.replace(/[^\d.]/g, '')"
              :parser="value => value.replace(/[^\d.]/g, '')"
            />
          </a-form-item>
          <!-- 关税税率 -->
          <a-form-item name="tariffRate" :label="'关税税率'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.tariffRate" style="width: 100%" :precision="6"/>
          </a-form-item>
          <!-- 关税金额 -->
          <a-form-item name="tariffAmount" :label="'关税金额'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.tariffAmount" style="width: 100%" :precision="2"/>
          </a-form-item>
          <!-- 消费税率 -->
          <a-form-item name="consumptionTaxRate" :label="'消费税率'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.consumptionTaxRate" style="width: 100%" :precision="6"/>
          </a-form-item>
          <!-- 消费税金额 -->
          <a-form-item name="consumptionTaxAmount" :label="'消费税金额'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.consumptionTaxAmount" style="width: 100%" :precision="6"/>
          </a-form-item>
          <!-- 增值税税率 -->
          <a-form-item name="vatRate" :label="'增值税税率'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.vatRate" style="width: 100%" :precision="6"/>
          </a-form-item>
          <!-- 增值税金额 -->
          <a-form-item name="vatAmount" :label="'增值税金额'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.vatAmount" style="width: 100%" :precision="6"/>
          </a-form-item>
          <!-- 进出口代理费率 -->
          <a-form-item name="importExportAgencyRate" :label="'进出口代理费率'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.importExportAgencyRate" style="width: 100%" :precision="5"/>
          </a-form-item>
          <!-- 进出口代理费 -->
          <a-form-item name="importExportAgencyFee" :label="'进出口代理费'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.importExportAgencyFee" style="width: 100%" :precision="2"/>
          </a-form-item>
          <!-- 总部代理费率 -->
          <a-form-item name="headquartersAgencyRate" :label="'总部代理费率'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.headquartersAgencyRate" style="width: 100%" :precision="4"/>
          </a-form-item>
          <!-- 总部代理费 -->
          <a-form-item name="headquartersAgencyFee" :label="'总部代理费'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.headquartersAgencyFee" style="width: 100%" :precision="2"/>
          </a-form-item>
          <!-- 合同数量 -->
          <a-form-item name="contractQuantity" :label="'合同数量'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.contractQuantity" style="width: 100%" :precision="4"/>
          </a-form-item>
          <!-- 计费重量 -->
          <a-form-item name="billingWeight" :label="'计费重量'" class="grid-item" :colon="false">
            <a-input-number
              disabled
              size="small"
              v-model:value="formData.billingWeight"
              style="width: 100%"
              :precision="4"
              :controls="false"
              :min="0"
              :formatter="value => value.replace(/[^\d.]/g, '')"
              :parser="value => value.replace(/[^\d.]/g, '')"
            />
          </a-form-item>
          <!-- 清关费 -->
          <a-form-item name="customsClearanceFee" :label="'清关费'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.customsClearanceFee" style="width: 100%" :precision="4"/>
          </a-form-item>
          <!-- 集装箱检验费 -->
          <a-form-item name="containerInspectionFee" :label="'集装箱检验费'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.containerInspectionFee" style="width: 100%" :precision="4"/>
          </a-form-item>
          <!-- 货运代理费 -->
          <a-form-item name="freightForwarderFee" :label="'货运代理费'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.freightForwarderFee" style="width: 100%" :precision="2"/>
          </a-form-item>
          <!-- 保险费率 -->
          <a-form-item name="insuranceRate" :label="'保险费率'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.insuranceRate" style="width: 100%" :precision="4"/>
          </a-form-item>
          <!-- 保险费 -->
          <a-form-item name="insuranceFee" :label="'保险费'" class="grid-item" :colon="false">
            <a-input-number disabled size="small" v-model:value="formData.insuranceFee" style="width: 100%" :precision="2"/>
          </a-form-item>
          <!-- 人民币划款金额 -->
          <a-form-item name="paymentAmount" :label="'人民币划款金额'" class="grid-item" :colon="false">
            <a-input-number
              disabled
              size="small"
              v-model:value="formData.paymentAmount"
              style="width: 100%"
              :precision="2"
              :controls="false"
              :min="0"
              :formatter="value => value.replace(/[^\d.]/g, '')"
              :parser="value => value.replace(/[^\d.]/g, '')"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-card>

    <a-card size="small"  class="cs-card-form">
      <aggr-contract-detail-table
        ref="detailTableRef"
        :disabled="showDisable"
        :head-id="formData.id"
        :edit-config="props.editConfig"
        @change="handleDetailChange"
      />
    </a-card>
  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN';
import {
  // savePlanWithDetails,
  updateAggrContractWithDetails,
  insertAggrContractWithDetails,
  confirmAggrContract
} from "@/api/nonAuxiliaryMaterials/aggrContract/AggrContractApi";
import AggrContractDetailTable from './list/AggrContractDetailTable.vue';
import {getUserInfo} from "@/api/payment/payment_info";
import ycCsApi from "@/api/ycCsApi";
import {useIncomingCommon} from "@/view/dec/incoming/common/IncomingCommon";

const { getPCode } = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack','editShowBody']);

const onBack = (val) => {
  emit('onEditBack', val);
};

const onBackShowBody = (val,editStatus,headId) => {
  emit('editShowBody', val, editStatus, headId);
};

// 是否禁用
const showDisable = ref(false)
// 添加表头控制字段
const hasHeadControl = computed(() => {
  return (formData.hasHeadNotice === '1' || formData.isCopy ==='1')
})

// 计算最终禁用状态（结合显示模式和表头控制）
const fieldDisabled = computed(() => {
  return showDisable.value || hasHeadControl.value
})
// 添加表头按钮控制字段
const hasHeadButtonControl = computed(() => {
  return (formData.hasHeadNotice === '1')
})
// 计算按钮最终禁用状态（结合显示模式和表头控制）
const buttonDisabled = computed(() => {
  return showDisable.value || hasHeadButtonControl.value
})
//海关参数港口
const customsPortOptions = reactive([]);

const getCustomsPortOptions = async () => {
  const params = {
    paramsType: 'PORT',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/6`,
      params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        customsPortOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取港口数据失败');
    }
  } catch (error) {
    message.error('获取港口数据失败');
  }
}


const businessDistinction = reactive([
  {
    label:'上海业务',
    value:'0'
  },
  {
    label:'外地业务',
    value:'1'
  },
],)

const { priceTermList } = useIncomingCommon({ immediate: true });

const transportModeOptions = reactive([
  {
    label:'海运',
    value:'0'
  },
  {
    label:'空运',
    value:'1'
  },
  {
    label:'陆运',
    value:'2'
  },
])

const priceTermPortOptions = reactive([
  {
    label:'起运港',
    value:'0'
  },
  {
    label:'目的港',
    value:'1'
  },
])

// 城市选项
const cityOptions = reactive([])
const cityEnOptions = reactive([])
// 币种选项
const currencyOptions = reactive([])
// 客商选项
const buyerOptions = reactive([])
const sellerOptions = reactive([])

// 表单数据
const formData = reactive({
  id: '',
  businessType: '6', // 默认6非国营贸易进口辅料
  contractNo: '',
  buyer: '', // 买方
  supplier: '',
  signingDate: '',
  domesticPrincipal: '',
  transportMode: '0', // 默认0海运
  effectiveDate: '',
  expiryDate: '',
  signingLocationCn: '', // 签约地点
  signingLocationEn: '', // 签约地点英文
  portOfLoading: '',
  portOfDestination: '',
  customsPort: '', // 报关口岸
  paymentMethod: '',
  currency: '', // 币种
  priceTerm: '',
  priceTermPort: '1', // 默认目的港
  suggestedSigner: '',
  shortageOverflowPercent: null,
  remarks: '',
  versionNo: '1', // 默认版本1
  status: '0', // 默认编制状态
  confirmTime: '',
  apprStatus: '0', // 默认不涉及审批
  createtUserName: '',
  insertTime: '',
  // 费用相关字段
  contractAmount: null,
  exchangeRate: null,
  tariffRate: null,
  tariffAmount: null,
  consumptionTaxRate: null,
  consumptionTaxAmount: null,
  vatRate: null,
  vatAmount: null,
  importExportAgencyRate: null,
  importExportAgencyFee: null,
  headquartersAgencyRate: null,
  headquartersAgencyFee: null,
  contractQuantity: null,
  billingWeight: null,
  customsClearanceFee: null,
  containerInspectionFee: null,
  freightForwarderFee: null,
  insuranceRate: null,
  insuranceFee: null,
  paymentAmount: null,
  // 协议相关字段
  agreementNo: '', // 协议编号
  agreementSigningDate: '', // 签约日期
  agencyRate: null, // 代理费率
  suggestedAuthorizedSigner: '', // 建议授权签约人
  agreementTerms: '', // 协议条款
  agreementRemarks: '', // 备注
})

// 表单校验规则
const rules = {
  businessType: [
    {required: true, message: '请选择业务类型', trigger: 'change'}
  ],
  contractNo: [
    {required: true, message: '请输入合同号', trigger: 'blur'},
    {max: 60, message: '合同号不能超过60个字符', trigger: 'blur'}
  ],
  buyer: [
    {required: true, message: '请选择买方', trigger: 'change'}
  ],
  supplier: [
    {required: true, message: '请选择供应商', trigger: 'change'}
  ],
  signingDate: [
    {required: true, message: '请选择签约日期', trigger: 'change'}
  ],
  domesticPrincipal: [
    {required: true, message: '请选择国内委托方', trigger: 'change'}
  ],
  currency: [
    {required: true, message: '请选择币种', trigger: 'change'}
  ],
  versionNo: [
    {required: true, message: '请输入版本号', trigger: 'blur'},
  ],
  createtUserName: [
    {required: true, message: '请输入制单人', trigger: 'blur'},
  ],
  insertTime: [
    {required: true, message: '请选择制单时间', trigger: 'change'}
  ]
}

/*const locale = {
  lang: {
    locale: 'zh_CN',
    placeholder: 'Select date',
    rangePlaceholder: ['Start date', 'End date'],
    today: 'Today',
    now: 'Now',
    backToToday: 'Back to today',
    ok: 'Ok',
    clear: 'Clear',
    month: 'Month',
    year: 'Year',
    timeSelect: 'Select time',
    dateSelect: 'Select date',
    monthSelect: 'Choose a month',
    yearSelect: 'Choose a year',
    decadeSelect: 'Choose a decade',
    yearFormat: 'YYYY',
    dateFormat: 'M/D/YYYY',
    dayFormat: 'D',
    dateTimeFormat: 'M/D/YYYY HH:mm:ss',
    monthFormat: 'MMMM',
    monthBeforeYear: true,
    previousMonth: 'Previous month (PageUp)',
    nextMonth: 'Next month (PageDown)',
    previousYear: 'Last year (Control + left)',
    nextYear: 'Next year (Control + right)',
    previousDecade: 'Last decade',
    remark: 'remark',
    nextDecade: 'Next decade',
    previousCentury: 'Last century',
    nextCentury: 'Next century',
  },
}*/

// 表单引用
const formRef = ref()

// 表格引用
const detailTableRef = ref();

// 日期选择器本地化
const locale = zhCN;

// 处理明细数据变化
const handleDetailChange = (details) => {
  formData.details = details;
};

// 监听签约地点变化，自动填充英文签约地点
const handleCityChange = (newSignPlace) => {
  if (newSignPlace && cityEnOptions.length > 0) {
    // 根据选中的签约地点key从cityEnOptions中找到对应的英文名称
    const selectedCity = cityEnOptions.find(city => city.value === newSignPlace);
    if (selectedCity) {
      formData.signingLocationEn = selectedCity.label;
    }
  } else if (!newSignPlace) {
    // 如果清空了签约地点，也清空英文签约地点
    formData.signingLocationEn = '';
  }
};

// 修改保存处理函数
const handlerSave = async () => {
  try {
    // 先验证表头数据
    await formRef.value.validate();

    // 获取表体数据
    const details = detailTableRef.value.getTableData();

    // 构建完整的保存数据
    const saveData = {
      ...formData,
      details: details,
      id: formData.id // 确保 id 被正确传递
    };

    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      const res = await insertAggrContractWithDetails(saveData);
      if (res.success) {
        // 更新表单数据为后端返回的数据
        Object.assign(formData, res.data.head);
        // 更新表体数据
        // if (res.data.details) {
        //   detailTableRef.value.reloadData();
        // }
        message.success('保存成功')
        props.editConfig.editStatus = editStatus.EDIT
        props.editConfig.hasSaved = true;  // 设置保存状态
        onBackShowBody(true,editStatus.EDIT,formData.id);
      } else {
        message.error(res.message || '保存失败');
        // 确保在保存失败时不设置hasSaved标志
        props.editConfig.hasSaved = false;
      }
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      const res = await updateAggrContractWithDetails(saveData);
      if (res.success) {
        // 更新表单数据为后端返回的数据
        Object.assign(formData, res.data.head);
        // 更新表体数据
        // if (res.data.details) {
        //   detailTableRef.value.reloadData();
        // }
        message.success('保存成功!')
        props.editConfig.hasSaved = true;  // 设置保存状态
      } else {
        message.error(res.message || '保存失败');
        // 确保在保存失败时不设置hasSaved标志
        props.editConfig.hasSaved = false;
      }
    }
  } catch (error) {
    console.error('保存失败:', error);
    // 如果是表单验证错误，不显示错误消息，因为表单验证会自动显示错误
    if (!error.errorFields) {
      message.error('保存失败，请检查必填项');
    }
    // 确保在保存失败时不设置hasSaved标志
    props.editConfig.hasSaved = false;
  }
}

// 修改保存并关闭处理函数
const handlerSaveClose = async () => {
  try {
    // 先验证表头数据
    await formRef.value.validate();

    // 获取表体数据
    const details = detailTableRef.value.getTableData();

    // 构建完整的保存数据
    const saveData = {
      ...formData,
      details: details
    };

    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      const res = await insertAggrContractWithDetails(saveData);
      if (res.success) {
        // 更新表单数据为后端返回的数据
        Object.assign(formData, res.data);
        message.success('保存成功')
        props.editConfig.hasSaved = true;  // 设置保存状态
        onBack(true)
      } else {
        message.error(res.message || '保存失败');
        // 确保在保存失败时不设置hasSaved标志
        props.editConfig.hasSaved = false;
      }
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      const res = await updateAggrContractWithDetails(saveData);
      if (res.success) {
        // 更新表单数据为后端返回的数据
        Object.assign(formData, res.data);
        message.success('保存成功!')
        props.editConfig.hasSaved = true;  // 设置保存状态
        onBack(true)
      } else {
        message.error(res.message || '保存失败');
        // 确保在保存失败时不设置hasSaved标志
        props.editConfig.hasSaved = false;
      }
    }
  } catch (error) {
    console.error('保存失败:', error);
    // 如果是表单验证错误，不显示错误消息，因为表单验证会自动显示错误
    if (!error.errorFields) {
      message.error('保存失败，请检查必填项');
    }
    // 确保在保存失败时不设置hasSaved标志
    props.editConfig.hasSaved = false;
  }
}

const getUpdateUser = () => {
  getUserInfo().then((res)=>{
    if (res.data !=null&&formData.createtUserName===''){
      formData.createtUserName =  res.data.userName
    }
  })
}


const pCode = ref('')

//获取城市参数
const getCityOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.city.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        cityOptions.push({
          value: item.paramCode,
          label: item.cityCnName
        });
        cityEnOptions.push({
          value: item.paramCode,
          label: item.cityEnName
        })
      });
    } else {
      message.error(res.message || '获取城市数据失败');
    }
  } catch (error) {
    message.error('获取城市数据失败');
  }
}

//基础资料-客商信息
const getBuyerOptions = async () => {
  try {
    const params = {
      merchantType: '0',
      commonFlag: '6'
    }
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

const getSellerOptions = async () => {
  try {
    const params = {
      merchantType: '1',
      commonFlag: '6'
    }
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        sellerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

//海关参数币制
const getCurrency = async () => {
  const params = {
    paramsType: 'CURR',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/6`,
      params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        currencyOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取币种数据失败');
    }
  } catch (error) {
    message.error('获取币种数据失败');
  }
}

// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    pCode.value = res;
  })
  // 获取港口数据
  getCustomsPortOptions();
  // 获取币种数据
  getCurrency();
  //获取客商信息
  getBuyerOptions();
  getSellerOptions();
  //获取城市
  getCityOptions();

  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    // 确保从 editConfig 中获取 id
    if (props.editConfig.editData && props.editConfig.editData.id) {
      formData.id = props.editConfig.editData.id;
    }
    Object.assign(formData, props.editConfig.editData || {});

/*    // 设置签约日期为当前日期
    if (!formData.signingDate) {
      formData.signingDate = new Date().toISOString().split('T')[0];
    }*/
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  if (formData.createTime === '') {
    const now = new Date();
    // 年-月-日
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需加1
    const day = String(now.getDate()).padStart(2, '0');
    // 时:分:秒
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    // 组合成目标格式
    formData.createTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  getUpdateUser()
})

// 在 script setup 部分添加新的响应式变量
const showFeeCard = ref(false)
const feeFormRef = ref()
const confirmLoading = ref(false)

// 切换费用卡片显示状态的方法
const toggleFeeCard = () => {
  showFeeCard.value = !showFeeCard.value
}

// 确认处理函数
const handlerConfirm = () => {
  // 检查当前状态
  if (formData.status === '1') {
    message.warning('该数据已经确认，无需重复操作');
    return;
  }
  if (formData.status === '2') {
    message.warning('该数据已经作废，不允许确认');
    return;
  }

  Modal.confirm({
    title: '确认操作',
    content: '是否确认所选项？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      confirmLoading.value = true;
      confirmAggrContract(formData.id).then(res => {
        if (res.code === 200) {
          message.success("确认成功！");
          // 更新本地状态
          formData.status = '1';
          formData.confirmTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
          // 更新editConfig中的数据
          if (props.editConfig && props.editConfig.editData) {
            props.editConfig.editData.status = '1';
            props.editConfig.editData.confirmTime = formData.confirmTime;
          }
        } else {
          message.error(res.message || "确认失败，请重试！");
        }
      }).catch(error => {
        message.error("请求失败，请重试！");
      }).finally(() => {
        confirmLoading.value = false;
      });
    }
  });
}
</script>
