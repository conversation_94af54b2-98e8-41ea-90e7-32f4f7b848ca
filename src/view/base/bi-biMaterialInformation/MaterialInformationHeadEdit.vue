<template>
  <section  >
<!--    <div class="tag-card">
      客户基础信息
    </div>
    <div class="cs-divider"></div>-->
    <a-card size="small" title="物料信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '180px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
          <a-form-item name="gName" :label="'商品名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.gName" @input="inputGName"/>
          </a-form-item>
          <a-form-item name="shortCn" :label="'中文简称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.shortCn"/>
          </a-form-item>

          <a-form-item name="billingName" :label="'开票名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.billingName"/>
          </a-form-item>

          <a-form-item name="fullEnName" :label="'英文全称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.fullEnName"/>
          </a-form-item>

          <a-form-item name="shortEnName" :label="'英文简称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.shortEnName"/>
          </a-form-item>

          <a-form-item name="merchandiseCategories" :label="'商品类别'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.merchandiseCategories" id="merchandiseCategories">
              <a-select-option v-for="item in merchandiseCategoriesMap"  :key="item.label  " :value="item.value" :label="item.label">
               {{item.label }}
              </a-select-option>
            </cs-select>
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.merchandiseCategories"/>-->
          </a-form-item>

          <a-form-item name="supplierCode" :label="'供应商'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.supplierCode" id="customsCreditRating">
              <a-select-option v-for="item in supplierCodeMap"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="supplierDiscountRate" :label="'供应商折扣率'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small" v-model:value="formData.supplierDiscountRate"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>

          <a-form-item name="importUnitPrice" :label="'进口单价'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              // 分别处理整数部分和小数部分
                              const parts = value.toString().split('.');
                              // 只对整数部分添加千位分隔符
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              // 组合整数和小数部分
                              return parts.join('.');
                            }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.importUnitPrice" notConvertNumber decimal int-length="11" precision="8"/>
          </a-form-item>

          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.curr"
              :disabled="showDisable"
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currMap"
            ></a-select>
<!--            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.curr" id="curr">-->
<!--              <a-select-option v-for="item in props.editConfig.currMap"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">-->
<!--                {{item.value}} {{item.label }}-->
<!--              </a-select-option>-->
<!--            </cs-select>-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.curr"/>-->
          </a-form-item>

          <a-form-item name="nationalProductCatalogue" :label="'国家产品目录'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.nationalProductCatalogue"/>
          </a-form-item>

          <a-form-item name="barCode" :label="'条形码'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.barCode"/>
          </a-form-item>

          <a-form-item name="commonMarkList" :label="'常用标志'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.commonMarkList"
              :disabled="showDisable"
              mode="multiple"
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="commonMarkMap"
            ></a-select>
          </a-form-item>

          <a-form-item name="packagingInformation" :label="'包装信息'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.packagingInformation" id="packagingInformation">
              <a-select-option v-for="item in packagingInformationMap"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.packagingInformation"/>-->
          </a-form-item>

          <a-form-item name="misCode" :label="'中烟MIS编码'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.misCode"/>
          </a-form-item>

          <a-form-item name="statisticalName" :label="'统计名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.statisticalName"/>
          </a-form-item>

          <a-form-item name="nameMethod" :label="'报送税务总局牌号名称方式'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.nameMethod" id="nameMethod">
              <a-select-option v-for="item in productClassify.nameMethodType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.nameMethod"/>-->
          </a-form-item>

<!--          <a-form-item name="taxExclusive" :label="'国内不含税调拨价（RMB）'" class="grid-item" :colon="false">-->
<!--            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small" v-model:value="formData.taxExclusive" notConvertNumber decimal int-length="19" precision="6"/>-->
<!--          </a-form-item>-->

          <a-form-item name="includingTax" :label="'国内含税调拨价(RMB)'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small" v-model:value="formData.includingTax"
                            :formatter="value => {
                              if (!value) return '0';
                              // 分别处理整数部分和小数部分
                              const parts = value.toString().split('.');
                              // 只对整数部分添加千位分隔符
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              // 组合整数和小数部分
                              return parts.join('.');
                            }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @input="onInputChange" notConvertNumber decimal int-length="11" precision="8"/>
          </a-form-item>

          <a-form-item name="taxRate" :label="'税率'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small" v-model:value="formData.taxRate" @input="onInputChange"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            notConvertNumber decimal int-length="13" precision="6"
            />
          </a-form-item>

          <a-form-item name="priceExcludingTax" :label="'国内不含税调拨价(RMB)'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small" :value="formData.priceExcludingTax"
                            :formatter="value => {
                              if (!value) return '0';
                              // 分别处理整数部分和小数部分
                              const parts = value.toString().split('.');
                              // 只对整数部分添加千位分隔符
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              // 组合整数和小数部分
                              return parts.join('.');
                            }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            notConvertNumber decimal int-length="11" precision="8"/>
          </a-form-item>

          <a-form-item name="note" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"/>
          </a-form-item>

<!--            <a-form-item label="多选标签" name="commonMark">-->
<!--              <MultiSelectDropdown v-model="formData.commonMark" :options="optionsa" />-->
<!--            </a-form-item>-->
          <a-form-item name="insertUserName"   :label="'创建人'" class="grid-item"  :colon="false">
            <a-input :disabled="true"  size="small" v-model:value="formData.insertUserName" />
          </a-form-item>
          <!-- 订单制单时间 -->
          <a-form-item name="insertTime"   :label="'创建时间'" class="grid-item"  :colon="false">
            <a-input :disabled="true"  size="small" v-model:value="formData.insertTime" />
          </a-form-item>
          <a-form-item name="dataState" :label="'数据状态'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.dataState" id="dataState">
              <a-select-option v-for="item in productClassify.dataStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
            <!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.dataState"/>-->
          </a-form-item>



          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>

        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref,computed} from "vue";
import {usePCode} from "@/view/common/usePCode";
import {MaterialInformationInsertClient, MaterialInformationUpdateClient,getMerchantCodeValueClient} from "@/api/bi/bi_client_info";
const { getPCode } = usePCode()
import VueMultiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.min.css';
import CsSelect from "@/components/select/CsSelect.vue";
import MultiSelectDropdown from './MultiSelectDropdown.vue';

const optionsa = [
  { value: 'option1', label: '选项 1' },
  { value: 'option2', label: '选项 2' },
  { value: 'option3', label: '选项 3' }
];
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
const onBack = (val) => {
  emit('onEditBack', val);
};

const value = ref()
const options= ref( ['Select option', 'options', 'selected', 'multiple', 'label', 'searchable', 'clearOnSelect', 'hideSelected', 'maxHeight', 'allowEmpty', 'showLabels', 'onChange', 'touched']
)
// const emit = defineEmits(['onBack']);
// const onBack = (val) => {
//   emit('onBack', val);
// };

// 是否禁用
const showDisable = ref(false)


// 表单数据
const formData = reactive({
  sid: '',
  businessType: '',
  dataStatus: '',
  versionNo: '',
  tradeCode: '',
  parentId: '',
  insertUser: '',
  insertTime: '',
  insertUserName: '',
  updateUserName: '',
  extend1: '',
  extend2: '',
  extend3: '',
  extend4: '',
  extend5: '',
  extend6: '',
  extend7: '',
  extend8: '',
  extend9: '',
  extend10: '',
  gName: '',
  shortCn: '',
  billingName: '',
  fullEnName: '',
  shortEnName: '',
  merchandiseCategories: '',
  nationalProductCatalogue: '',
  barCode: '',
  commonMark: '',
  packagingInformation: '',
  misCode: '',
  statisticalName: '',
  nameMethod: '',
  taxExclusive: '',
  includingTax: '',
  taxRate: '13',
  priceExcludingTax: '',
  note: '',
  dataState: '0',
  supplierCode: '',
  supplierDiscountRate: '',
  importUnitPrice: '',
  curr: '',
  commonMarkList:[]
})
// 校验规则
const rules = {
  businessType: [
    { max: 60, message: '长度不能超过60位字节(汉字占2位)！', trigger: 'blur' }
  ],
  dataStatus: [
    { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
  ],
  versionNo: [
    { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
  ],
  tradeCode: [
    { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
  ],
  parentId: [
    { max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
  ],
  insertUser: [
    // { required: true, message: '创建人不能为空！', trigger: 'blur' },
    { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
  ],
  insertTime: [
    // { required: true, message: '创建日期不能为空！', trigger: 'blur' },
    { max: null, message: '长度不能超过null位字节(汉字占2位)！', trigger: 'blur' }
  ],
  insertUserName: [
    { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
  ],
  updateUserName: [
    { max: 50, message: '长度不能超过50位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend1: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend2: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend3: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend4: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend5: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend6: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend7: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend8: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend9: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  extend10: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  gName: [
    { required: true, message: '商品名称不能为空！', trigger: 'blur' },
    { max: 80, message: '长度不能超过80位字节(汉字占2位)！', trigger: 'blur' }
  ],
  shortCn: [
    { max: 80, message: '长度不能超过80位字节(汉字占2位)！', trigger: 'blur' }
  ],
  billingName: [
    { max: 80, message: '长度不能超过80位字节(汉字占2位)！', trigger: 'blur' }
  ],
  fullEnName: [
    { max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
  ],
  shortEnName: [
    { max: 80, message: '长度不能超过80位字节(汉字占2位)！', trigger: 'blur' }
  ],
  merchandiseCategories: [
    { required: true, message: '商品类别不能为空！', trigger: 'blur' },
    { max: 80, message: '长度不能超过80位字节(汉字占2位)！', trigger: 'blur' }
  ],
  nationalProductCatalogue: [
    { max: 100, message: '长度不能超过100位字节(汉字占2位)！', trigger: 'blur' }
  ],
  barCode: [
    { max: 80, message: '长度不能超过80位字节(汉字占2位)！', trigger: 'blur' }
  ],
  commonMark: [
    { max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
  ],
  packagingInformation: [
    { max: 40, message: '长度不能超过40位字节(汉字占2位)！', trigger: 'blur' }
  ],
  misCode: [
    { max: 80, message: '长度不能超过80位字节(汉字占2位)！', trigger: 'blur' }
  ],
  statisticalName: [
    { max: 80, message: '长度不能超过80位字节(汉字占2位)！', trigger: 'blur' }
  ],
  nameMethod: [
    { max: 30, message: '长度不能超过30位字节(汉字占2位)！', trigger: 'blur' }
  ],
  taxExclusive: [
  ],
  includingTax: [
  ],
  taxRate: [
  ],
  priceExcludingTax: [
  ],
  note: [
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  dataState: [
    { required: true, message: '数据状态不能为空！', trigger: 'blur' },
    { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
  ],
  supplierCode: [
    { required: true, message: '供应商不能为空！', trigger: 'blur' },
    { max: 200, message: '长度不能超过200位字节(汉字占2位)！', trigger: 'blur' }
  ],
  supplierDiscountRate: [
  ],
  importUnitPrice: [
  ],
  curr: [
    { required: true, message: '币种不能为空！', trigger: 'blur' },
    { max: 10, message: '长度不能超过10位字节(汉字占2位)！', trigger: 'blur' }
  ]
}
const commonMarkMap = ref([
  {
    label: "国营贸易进口卷烟",
    value: "1"
  },
  {
    label: "国营贸易进口辅料",
    value: "2"
  },
  {
    label: "国营贸易进口卷烟设备",
    value: "3"
  },
  {
    label: "国营贸易进口丝束",
    value: "4"
  },
  {
    label: "国营贸易内购内销丝束",
    value: "5"
  },
  {
    label: "非国营贸易进口辅料",
    value: "6"
  },
  {
    label: "出料加工进口薄片",
    value: "7"
  },
  {
    label: "出口烟机设备",
    value: "8"
  },
  {
    label: "出口辅料",
    value: "9"
  }
])
// const supplierCodeMap = ref([
//   {
//     label:'供应商',
//     value:'PRD'
//   },
//   {
//     label:'客户',
//     value:'CLI'
//   },
//   {
//     label:'货代',
//     value:'FOD'
//   },
//   {
//     label:'报关行',
//     value:'CUT'
//   },
//   {
//     label:'企业',
//     value:'COM'
//   }
// ])
const supplierCodeMap = ref([])
const packagingInformationMap = ref([])
const currMap = ref([])
const merchandiseCategoriesMap = ref([])
const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
    currMap.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      value
    }));
  })
  getMerchantCodeValueClient().then((res)=>{
    if (res.code === 200){
      //供应商
      if (typeof(res.data.supplierCodeMap) !== "undefined"){
        res.data.supplierCodeMap.map(item => {
          supplierCodeMap.value.push({
            label: item.label,
            value: item.value
          })
        })
      }

      //包装信息
      if (typeof(res.data.packagingInformation) !== "undefined"){
        res.data.packagingInformation.map(item => {
          packagingInformationMap.value.push({
            label: item.value,
            value: item.label
          })
        })
      }
      //商品类别
      if (typeof(res.data.merchandiseCategories) !== "undefined"){
        res.data.merchandiseCategories.map(item => {
          merchandiseCategoriesMap.value.push({
            label: item.value,
            value: item.label
          })
        })
      }
    }
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    Object.assign(formData, {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
    resetSearch()
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
    resetSearch()
  }
/*  getMerchantCodeValueClient().then((res)=>{
    if (res.code === 200){
      //供应商
      if (typeof(res.data.supplierCodeMap) !== "undefined"){
      supplierCodeMap.value=res.data.map(item => {
        return {
          label: item.label,
          value: item.value
        }
      })
    }}
  })*/
});

const updateValueAction = ({ commit }, value) =>{
  emit('updateValue', value)
}

const resetSearch = () => {
  Object.keys(formData).forEach(key => {
    if(formData[key] === null || formData[key] === undefined){
      if (key === 'commonMarkList'){
        formData[key] = [];
      }else {
        formData[key] = '';
      }
    }
  });
}
defineExpose({formData,resetSearch});

// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        MaterialInformationInsertClient(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            props.editConfig.editStatus = editStatus.EDIT
            formData.sid = res.data.sid
            formData.insertTime = res.data.insertTime
            formData.insertUser = res.data.insertUser
            formData.insertUserName = res.data.insertUserName
            formData.tradeCode = res.data.tradeCode
            // onBack(false)
          } else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        console.log('value',formData)
        MaterialInformationUpdateClient(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            // onBack(false)
          } else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

const inputGName = () =>{
  if(formData.gName !== null){
    formData.billingName = formData.gName;
  }
}
const onInputChange = () =>{
  if(formData.priceExcludingTax !== null){
    delete formData.priceExcludingTax;
  }
  if(formData.taxRate !== null && formData.includingTax !== null){
    formData.priceExcludingTax = taxCount(formData)
  }
}
const taxCount = (row) => {
  const taxRate = parseFloat(row.taxRate);
  const includingTax = parseFloat(row.includingTax);
  const priceExcludingTax = roundToDecimal(includingTax/(100+taxRate)*100,8)
  return priceExcludingTax !== null ? priceExcludingTax : null
};
function roundToDecimal(num, decimals) {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}

// 格式化显示的计算属性
const formattedValues = {
  importUnitPrice: computed(() => formatNumber(formData.importUnitPrice)),
  includingTax: computed(() => formatNumber(formData.includingTax)),
  priceExcludingTax: computed(() => formatNumber(formData.priceExcludingTax)),

}
// 格式化数字的辅助函数
const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') {
    return '0';
  }
  // 将值转换为数字
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '0';
  }
  // 使用 toLocaleString 添加千位分隔符
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};


</script>

<style lang="less" scoped>


</style>



