<template>
  <section  >

    <a-card size="small" title="付款通知" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <a-form-item name="docNo" :label="'单据号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.docNo"/>
          </a-form-item>

          <a-form-item name="bizType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.bizType" id="bizType">
              <a-select-option v-for="item in productClassify.businessType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!--          预付标志-->
          <a-form-item name="prepayFlag" :label="'预付标志'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.prepayFlag" id="prepayFlag">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!--          部门-->
          <a-form-item name="department" :label="'部门'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.department"/>
          </a-form-item>

          <a-form-item name="curr"   :label="'付款币种'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.curr" @change="getRate" id="curr">
              <a-select-option    v-for="item in currList"  :key="item.label  " :value="item.value" :label="item.label">
                {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="payAmt" :label="'金额'" class="grid-item" :colon="false">
            <a-input-number :disabled="true" size="small" v-model:value="formattedValues.payAmt.value" style="width: 100%"/>
          </a-form-item>
          <a-form-item name="rate" :label="'汇率'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.rate" style="width: 100%" @pressEnter="calculateRmbAmount"/>
          </a-form-item>

          <!--          收款方-->
          <a-form-item name="payee" :label="'收款方'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.payee" id="payee">
              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="payAmtRmb" :label="'RMB金额'" class="grid-item" :colon="false">
            <a-input-number :disabled="true" size="small" v-model:value="formattedValues.payAmtRmb.value" style="width: 100%"/>
          </a-form-item>

          <a-form-item name="bizDate" :label="'业务日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.bizDate"
              id="bizDate"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>

          <a-form-item name="recvBank" :label="'收款银行'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.recvBank"/>
          </a-form-item>

          <a-form-item name="recvAcct" :label="'收款方帐号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.recvAcct"/>
          </a-form-item>

          <!--          发送用友-->
          <a-form-item name="sendUfida" :label="'发送用友'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendUfida" id="sendUfida">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!--          是否红冲-->
          <a-form-item name="redFlush" :label="'是否红冲'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.redFlush" id="redFlush">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="note" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"/>
          </a-form-item>

          <!--          单据状态-->
          <a-form-item name="docStatus" :label="'单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.docStatus" id="docStatus">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!--          制单人-->
          <a-form-item name="updateUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.updateUserName"/>
          </a-form-item>
          <!--          制单日期-->
          <a-form-item name="updateTime" :label="'制单日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.updateTime"
              id="updateTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!--          确认时间-->
          <a-form-item name="cfmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.cfmTime"
              id="cfmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" :loading="saveLoading" @click="handlerSave" class="cs-margin-right"
                        v-show="props.editConfig.editStatus !== editStatus.SHOW ">保存
            </a-button>
<!--            <a-button size="small" type="primary" :loading="saveColseLoading" @click="handlerSaveClose" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存关闭-->
<!--            </a-button>-->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            <a-button size="small" type="primary" @click="handlerConfirm" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.EDIT">确认
            </a-button>
<!--            <a-button size="small" type="primary" @click="handlerAddList" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus === editStatus.EDIT">新增明细-->
<!--            </a-button>-->
          </div>
        </a-form>
      </div>
    </a-card>
    <a-card size="small" v-if="props.editConfig.editStatus !== editStatus.ADD && showState" class="cs-card-form">
      <NotifyList
        ref="detailTableRef"
        :disabled="showDisable"
        :head-id="formData.sid"
        :edit-config="props.editConfig"
        @onHeadback="headBack"
      />
    </a-card>

<!--    <div>-->
<!--      &lt;!&ndash; 使用原生 <dialog> 元素 &ndash;&gt;-->
<!--      <cs-modal :visible="open" :title="'新增明细'" style="width: 80%" :footer="false" :closable="false">-->
<!--        <template #customContent>-->
<!--          <NotifyDetailTab v-if="modelView" :editConfig="props.editConfig" @onEditBack="handleCancel" @onHeadback="headBack" />-->
<!--        </template>-->
<!--      </cs-modal>-->
<!--    </div>-->

  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, nextTick, createVNode, computed, watch} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {
  insertNotifyHead,
  updateNotifyHead,
  getDocNoNotifyHead,
  getUserInfo,
  confirmContract, getRateHead
} from "@/api/payment/payment_info";
const { getPCode } = usePCode()
import ycCsApi from "@/api/ycCsApi";
import NotifyList from "@/view/payment/notify/list/NotifyList.vue";
import {updateContract} from "@/api/importedCigarettes/contract/contractApi";
import CsModal from "@/components/modal/cs-modal.vue";
import NotifyDetailTab from "@/view/payment/notify/DetailTab/NotifyDetailTab.vue";
import {useCommon} from "@/view/common/useCommon";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {getCurrList} from "@/api/params/params_info";

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

const {
  editConfig,
  // show,
  page,
  showSearch,
  getList,
  headSearch,
  // handleEditByRow,
  // handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData

} = useCommon()

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack','onHeadback','headId']);
const onBack = (val) => {
  emit('onEditBack', val);
};

// 是否禁用
const showDisable = ref(false)
const showState = ref(true)

// 表单数据
const formData = reactive({
  sid:'',
  bizType: '1',
  payee: '中国烟草国际有限公司',
  contractNo: '',
  orderNumber: '',
  payAmt: 0,
  curr: 'CNY',
  prepayFlag: '0',
  sendUfida: '0',
  redFlush: '1',
  docStatus: '0',
  cfmTime: '',
  bizDate: '',
  recvBank: '中国工商银行股份有限公司北京白云路支行',
  recvAcct: '0200020009004631536',
  payAmtRmb: 0,
  rate: '',
  docNo: '',
  department: '业务一部',
  updateUserName: '',
  updateTime: '',
  note: '',


})
// 校验规则
const rules = {
  docNo:[
    {required: true, message: '不能为空', trigger: 'blur'},
  ],

  bizType: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  prepayFlag: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  redFlush: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  department: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  curr: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  payAmt: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  rate: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  payee: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  payAmtRmb: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  bizDate: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  updateUserName: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  updateTime: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],


}
const currList = ref([])
const rateList = ref([])
const pCode = ref('')
const detailTableRef = ref();
const open = ref(false)
const modelView = ref(false);
const tableData = ref([]);
const editableKeys = ref([]);
//基础资料-客商信息
const buyerOptions = reactive([])


// 格式化显示的计算属性
const formattedValues = {
  payAmt: computed(() => formatNumber(formData.payAmt)),
  payAmtRmb: computed(() => formatNumber(formData.payAmtRmb)),
}


// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }

  // 转换为数字并检查有效性
  const number = Number(value);
  if (isNaN(number)) {
    return '';
  }

  // 配置 NumberFormat 选项
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,  // 至少两位小数
    maximumFractionDigits: 10
  }).format(number);
};

const handleCancel = async () => {
  modelView.value = false;
  open.value = false;
  // 等待 DOM 更新完成
  await nextTick();
  if (detailTableRef.value) {
    detailTableRef.value.getList();
  }
};

const headBack = async (val) =>{
  formData.payAmt = val.payAmt
  formData.payAmtRmb = val.payAmtRmb
  if (val.payee !=null){
    formData.payee = val.payee
  }
}


const getBuyerOptions = async () => {
  try {
    const params = {
      merchantType: '0',
      commonFlag: formData.bizType || ''
    }
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 清空原有数据
      buyerOptions.length = 0;
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取收款方数据失败');
    }
  } catch (error) {
    message.error('获取收款方数据失败');
  }
}



// 计算 RMB 金额
const calculateRmbAmount = async () => {
  // 确保数值有效性
  if (typeof formData.payAmt === 'number' && typeof formData.rate === 'number') {
    formData.payAmtRmb = formData.payAmt * formData.rate;
    // 保留两位小数
    formData.payAmtRmb = Number(formData.payAmtRmb.toFixed(2));
  }
};




const saveLoading = ref(false);
const saveColseLoading = ref(false);

// 初始化操作
onMounted(() => {


  getCurrency()

  getBuyerOptions();

  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false

    Object.assign(formData, {});
    const now = new Date();

// 年-月-日
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需加1
    const day = String(now.getDate()).padStart(2, '0');

// 时:分:秒
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

// 组合成目标格式
    formData.bizDate =  `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    formData.updateTime =  `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    getDocNo()
    getUpdateUser()
    getRate()


  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
});




// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);

// 确认

/* 确认事件 */
const handlerConfirm = () => {
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '确认执行此操作吗？',
    onOk() {

      // 这里需要调用确认API
      const params = {
        sid : formData.sid
      }
      confirmContract(params).then(res => {
        if (res.code === 200) {
          message.success("确认成功！")
          formData.docStatus = '1'
          props.editConfig.editStatus = editStatus.SHOW
          showDisable.value = true
          if (detailTableRef.value) {
            detailTableRef.value.getList();
          }
        }else {
          message.error(res.message)
        }
      }).finally(() => {

      })
    },
    onCancel() {
      // 取消操作
    },
  });
}


// 保存
const handlerSave = async () => {
  formRef.value
    .validate()
    .then(() => {
      saveLoading.value = true;
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        calculateRmbAmount()
        insertNotifyHead(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            formData.sid = res.data.sid
            formData.docNo = res.data.docNo
            props.editConfig.editData.sid = res.data.sid
            props.editConfig.editData.bizType = res.data.bizType
            emit('headId', res.data.sid)
            props.editConfig.editStatus = editStatus.EDIT
            // 保存成功后刷新明细表格数据
            if (detailTableRef.value) {
              detailTableRef.value.getList();
            }
            saveLoading.value = false;
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        calculateRmbAmount()
        showState.value = false
        updateNotifyHead(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            props.editConfig.editData.bizType = res.data.bizType
            // 保存成功后刷新明细表格数据
            if (detailTableRef.value) {
              detailTableRef.value.getList();
            }
            formData.updateUserName =  res.data.updateUserName
            saveLoading.value = false;
          }else {
            message.error(res.message)
          }
        }).finally(() => {
          showState.value = true;
        })
      }
    }).catch(error => {
      console.log('validate failed', error);
    })
};

const handlerSaveClose = async () => {
  formRef.value
    .validate()
    .then(() => {
      saveColseLoading.value = true
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        calculateRmbAmount()
        insertNotifyHead(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
            saveColseLoading.value = false
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        calculateRmbAmount()
        updateNotifyHead(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            onBack(true)
            saveColseLoading.value = false
          }else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
}

const getDocNo = () => {
  getDocNoNotifyHead().then((res)=>{
    if (res.data !=null){
      formData.docNo =  res.data
    }
  })
}
const getRate = () => {
  getRateHead(formData.curr).then((res)=>{
    if (res.data !=null){
      formData.rate =  Number(Number(res.data).toFixed(6))
      calculateRmbAmount()
    }else{
      formData.rate = null
    }
    if(res.code === 400){
      message.error(res.message)
    }
  })

}
const getCurrency = async () => {
  const params = {
    paramsType: 'CURR',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/${formData.bizType}`,
      params
    );
    if (res.code === 200) {
      currList.value = []
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        currList.value .push({
          value: item.key,
          label: item.key
        });
      });
    } else {
      message.error(res.message || '获取币制数据失败');
    }
  } catch (error) {
    message.error('获取币制数据失败');
  }
}
const getUpdateUser = () => {
  getUserInfo().then((res)=>{
    if (res.data !=null){
      formData.updateUserName =  res.data.userName
    }
  })
}

//当formData.bizType变化时先清空currList.value，重新调用getCurrency()
watch(() => formData.bizType, (newVal, oldVal) => {
  getCurrency()
},{immediate: true,deep:true})

//当formData.bizType变化时，动态获取收款方数据
watch(() => formData.bizType, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    // 重新获取收款方数据
    getBuyerOptions();
  }
}, {immediate: true, deep: true})



const handlerAddList = ()=>{
  modelView.value = true;
  open.value = true;
}



</script>

<style lang="less" scoped>


</style>



